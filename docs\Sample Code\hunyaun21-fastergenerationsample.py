import os
import sys
import argparse
from pathlib import Path
from PIL import Image
import trimesh

# Ensure vendor modules are importable when this file lives under AIStudio/
here = Path(__file__).resolve()
repo_root = None
for anc in here.parents:
    if (anc / 'pipelines').exists():
        repo_root = anc
        break
if repo_root is None:
    repo_root = here.parents[-1]

# Add vendor subpackages relative to the Hunyuan3D-2.1 folder
vendor_root = Path(__file__).parent.parent  # .../Hunyuan3D-2.1
sys.path.insert(0, str(vendor_root / 'hy3dshape'))

TEXTURE_SUPPORT = False
try:
    from hy3dshape.rembg import BackgroundRemover
    from hy3dshape.pipelines import Hunyuan3DDiTFlowMatchingPipeline
    TEXTURE_SUPPORT = False  # default off; enable only if paint is wired
except ImportError as e:
    print(f"Warning: Could not import Hunyuan3D modules: {e}")
    print("Falling back to shape generation only mode")

if TEXTURE_SUPPORT:
    try:
        sys.path.insert(0, str(vendor_root / 'hy3dpaint'))
        from textureGenPipeline import Hunyuan3DPaintPipeline, Hunyuan3DPaintConfig
    except ImportError as e:
        print(f"Warning: Could not import texture generation modules: {e}")
        TEXTURE_SUPPORT = False

# Optional torchvision fix
try:
    from torchvision_fix import apply_fix
    apply_fix()
except ImportError:
    pass
except Exception as e:
    print(f"Warning: Failed to apply torchvision fix: {e}")

class Hunyuan3DGenerator:
    def __init__(self, model_dir):
        self.model_dir = Path(model_dir)
        try:
            self.shape_pipeline = self._init_shape_pipeline()
            self.paint_pipeline = None
            self.rembg = BackgroundRemover() if 'BackgroundRemover' in globals() else None
        except Exception as e:
            print(f"Warning: Could not initialize all components: {e}")
            self.shape_pipeline = None
            self.paint_pipeline = None
            self.rembg = None

    def _init_shape_pipeline(self):
        if 'Hunyuan3DDiTFlowMatchingPipeline' not in globals():
            raise ImportError("Hunyuan3D shape generation module not available")
        print("Initializing Hunyuan3D generator with shape pipeline…")
        shape_model_path = self.model_dir / 'hunyuan3d-dit-v2-1'
        if not shape_model_path.exists():
            raise FileNotFoundError(f"Model directory not found: {shape_model_path}")
        return Hunyuan3DDiTFlowMatchingPipeline.from_pretrained(str(shape_model_path))

    def process_image(self, image_path, output_dir, texture=True):
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        print(f"Processing image: {image_path}")
        image = Image.open(image_path).convert('RGBA')
        try:
            if self.rembg and image.mode == 'RGBA' and image.getextrema()[3][0] < 255:
                print("Removing background…")
                image = self.rembg(image)
        except Exception as e:
            print(f"Warning: Could not remove background: {e}")

        print("Generating 3D shape…")
        mesh_path = str(output_dir / 'output_shape.glb')
        mesh = self.shape_pipeline(image=image)[0]
        if isinstance(mesh, trimesh.Trimesh):
            mesh.export(mesh_path)
        else:
            try:
                if hasattr(mesh, 'vertices') and hasattr(mesh, 'faces'):
                    trimesh.Trimesh(vertices=mesh.vertices, faces=mesh.faces).export(mesh_path)
                else:
                    mesh.export(mesh_path)
            except Exception as e:
                print(f"Warning: Could not convert mesh to standard format: {e}")
                raise
        print("3D shape saved")

        results = {'mesh': mesh_path}
        if texture and TEXTURE_SUPPORT and self.paint_pipeline is not None:
            textured_mesh_path = str(output_dir / 'output_textured.glb')
            try:
                self.paint_pipeline(mesh_path=mesh_path, image_path=image_path, output_mesh_path=textured_mesh_path)
                print("Textured mesh saved")
                results['textured_mesh'] = textured_mesh_path
            except Exception as e:
                print(f"Warning: Could not generate texture: {e}")
        return results


def main():
    parser = argparse.ArgumentParser(description='AIStudio wrapper for Hunyuan3D-2.1')
    parser.add_argument('--image', type=str, required=True)
    parser.add_argument('--output_dir', type=str, required=True)
    parser.add_argument('--model_dir', type=str, required=True)
    parser.add_argument('--no_texture', action='store_true')
    args = parser.parse_args()

    gen = Hunyuan3DGenerator(args.model_dir)
    if gen.shape_pipeline is None:
        print('Error: Could not initialize the 3D shape generation pipeline')
        return 1

    gen.process_image(args.image, args.output_dir, texture=not args.no_texture)
    return 0

if __name__ == '__main__':
    raise SystemExit(main())

