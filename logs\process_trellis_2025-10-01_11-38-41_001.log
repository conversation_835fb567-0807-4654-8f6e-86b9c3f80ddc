========================================
AIStudio Real-time Log: trellis
Started: 2025-10-01T16:38:41.410Z
File: process_trellis_2025-10-01_11-38-41_001.log
========================================

[2025-10-01T16:38:41.410Z] [STDOUT] DEBUG: VENV_PATH is "N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv"
[2025-10-01T16:38:41.412Z] [STDOUT] DEBUG: Checking for "N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv\Scripts\activate.bat"
[2025-10-01T16:38:41.412Z] [STDOUT] DEBUG: Virtual environment already exists, skipping creation
[2025-10-01T16:38:41.422Z] [STDOUT] 1 file(s) copied.
[2025-10-01T16:38:41.679Z] [STDOUT] _
[2025-10-01T16:38:41.680Z] [STDOUT] Current Python: "N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv\Scripts\python.exe"
[2025-10-01T16:38:41.681Z] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv
[2025-10-01T16:38:41.681Z] [STDOUT] _
[2025-10-01T16:38:41.687Z] [STDOUT] Current Python: "N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv\Scripts\python.exe"
[2025-10-01T16:38:41.688Z] [STDOUT] Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv
[2025-10-01T16:38:41.689Z] [STDOUT] Starting the server, please wait...
