========================================
AIStudio Real-time Log: main
Started: 2025-10-01T16:11:42.376Z
File: app_main_2025-10-01_11-11-42_001.log
========================================

[2025-10-01T16:11:42.856Z] [INFO] AIStudio application started successfully
[2025-10-01T16:11:42.856Z] [INFO] [main] AIStudio application started successfully
[2025-10-01T16:11:42.885Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-10-01T16:11:43.784Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-10-01T16:11:48.006Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-10-01_11-11-48_001.log
[2025-10-01T16:11:48.006Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-10-01T16:12:03.757Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_python installation_2025-10-01_11-12-03_001.log
[2025-10-01T16:12:03.757Z] [INFO] [dependency_core] DependencyManager: Starting python installation for Core
[2025-10-01T16:12:03.758Z] [INFO] [dependency_core] DependencyManager: Installing dependencies for Core (python:all)
[2025-10-01T16:12:03.758Z] [INFO] [dependency_core] DependencyManager: Component type: string, Component value: 'python'
[2025-10-01T16:12:03.759Z] [INFO] [dependency_core] DependencyManager: Name type: string, Name value: 'all'
[2025-10-01T16:12:03.759Z] [INFO] [dependency_core] DependencyManager: About to check routing for Core with component python
[2025-10-01T16:12:03.760Z] [INFO] [dependency_core] DependencyManager: Installing Python dependencies for Core...
[2025-10-01T16:16:03.949Z] [INFO] [dependency_core] DependencyManager: No models configured for Core
[2025-10-01T16:16:06.531Z] [INFO] [dependency_core] DependencyManager: Dependency status updated for Core
[2025-10-01T16:16:06.532Z] [INFO] [dependency_core] DependencyManager: Completed python installation for Core
[2025-10-01T16:16:07.034Z] [INFO] [dependency_core] DependencyManager: Sending dependency-status-changed event for Core (python)
[2025-10-01T16:16:07.540Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-10-01T16:16:19.477Z] [INFO] [RealtimeLogger] Started logging dependency_trellis_stable_projectorz_101 to: dependency_python installation_2025-10-01_11-16-19_001.log
[2025-10-01T16:16:19.477Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Starting python installation for trellis-stable-projectorz-101
[2025-10-01T16:16:19.477Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Installing dependencies for trellis-stable-projectorz-101 (python:all)
[2025-10-01T16:16:19.478Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Component type: string, Component value: 'python'
[2025-10-01T16:16:19.478Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Name type: string, Name value: 'all'
[2025-10-01T16:16:19.478Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: About to check routing for trellis-stable-projectorz-101 with component python
[2025-10-01T16:31:57.751Z] [INFO] [RealtimeLogger] Started logging dependency_v13_hunyuan2_stableprojectorz to: dependency_python installation_2025-10-01_11-31-57_001.log
[2025-10-01T16:31:57.752Z] [INFO] [dependency_v13_hunyuan2_stableprojectorz] DependencyManager: Starting python installation for v13_hunyuan2-stableprojectorz
[2025-10-01T16:31:57.752Z] [INFO] [dependency_v13_hunyuan2_stableprojectorz] DependencyManager: Installing dependencies for v13_hunyuan2-stableprojectorz (python:all)
[2025-10-01T16:31:57.752Z] [INFO] [dependency_v13_hunyuan2_stableprojectorz] DependencyManager: Component type: string, Component value: 'python'
[2025-10-01T16:31:57.753Z] [INFO] [dependency_v13_hunyuan2_stableprojectorz] DependencyManager: Name type: string, Name value: 'all'
[2025-10-01T16:31:57.753Z] [INFO] [dependency_v13_hunyuan2_stableprojectorz] DependencyManager: About to check routing for v13_hunyuan2-stableprojectorz with component python
[2025-10-01T16:38:40.291Z] [INFO] [RealtimeLogger] Started logging dependency_trellis_stable_projectorz_101 to: dependency_dependency check_2025-10-01_11-38-40_001.log
[2025-10-01T16:38:40.291Z] [INFO] [dependency_trellis_stable_projectorz_101] DependencyManager: Starting dependency check for trellis-stable-projectorz-101
[2025-10-01T16:38:40.296Z] [INFO] [34m[Trellis Server][39m 🚀 [Trellis Main] generate3DModel called with imagePath: N:\AIStudio\uploads\cf58e7e4-eb7f-46a1-8485-cba87aa21326\1757178564675_bg_removed_aztec-figurine-235-topaz-upscale-2.7x_processed.png
[2025-10-01T16:38:40.296Z] [INFO] [34m[Trellis Server][39m 🧹 [Trellis Init] Preparing environment for 3D generation
[2025-10-01T16:38:40.297Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-10-01T16:38:41.199Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-10-01T16:38:41.204Z] [INFO] [34m[Trellis Server][39m 🔄 [Trellis Progress] Stage tracking reset for new generation
[2025-10-01T16:38:41.204Z] [INFO] [34m[Trellis Server][39m 🎯 [Trellis Init] Starting fresh 3D generation pipeline
[2025-10-01T16:38:41.204Z] [INFO] [34m[Trellis Server][39m 🔧 [Trellis Server] Server not running, starting Trellis server...
[2025-10-01T16:38:41.205Z] [INFO] [IPC Handler] Sending status: sparse_structure - 0% - Generating 3D structure foundation
[2025-10-01T16:38:41.205Z] [INFO] [34m[Trellis Server][39m Starting server...
[2025-10-01T16:38:41.205Z] [INFO] [34m[Trellis Server][39m RUN_BAT: N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\run.bat
[2025-10-01T16:38:41.206Z] [INFO] [34m[Trellis Server][39m Batch file exists: true
[2025-10-01T16:38:41.206Z] [INFO] [34m[Trellis Server][39m Working directory: N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101
[2025-10-01T16:38:41.207Z] [INFO] [34m[Trellis Server][39m Command: cmd.exe /c run.bat
[2025-10-01T16:38:41.247Z] [INFO] [34m[Trellis Server][39m ✅ [Trellis Server] startTrellisServer() called successfully
[2025-10-01T16:38:41.410Z] [INFO] [34m[Trellis Server][39m DEBUG: VENV_PATH is "N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv"
[2025-10-01T16:38:41.410Z] [INFO] [RealtimeLogger] Started logging trellis to: process_trellis_2025-10-01_11-38-41_001.log
[2025-10-01T16:38:41.411Z] [INFO] [34m[Trellis Server][39m DEBUG: Checking for "N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv\Scripts\activate.bat"
[2025-10-01T16:38:41.412Z] [INFO] [34m[Trellis Server][39m DEBUG: Virtual environment already exists, skipping creation
[2025-10-01T16:38:41.422Z] [INFO] [34m[Trellis Server][39m 1 file(s) copied.
[2025-10-01T16:38:41.679Z] [INFO] [34m[Trellis Server][39m _
[2025-10-01T16:38:41.680Z] [INFO] [34m[Trellis Server][39m Current Python: "N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv\Scripts\python.exe"
[2025-10-01T16:38:41.680Z] [INFO] [34m[Trellis Server][39m Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv
[2025-10-01T16:38:41.681Z] [INFO] [34m[Trellis Server][39m _
[2025-10-01T16:38:41.687Z] [INFO] [34m[Trellis Server][39m Current Python: "N:\AIStudio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv\Scripts\python.exe"
[2025-10-01T16:38:41.688Z] [INFO] [34m[Trellis Server][39m Virtual Env: N:\3D AI Studio\pipelines\3DPipelines\gen3d\trellis-stable-projectorz-101\code\venv
[2025-10-01T16:38:41.689Z] [INFO] [34m[Trellis Server][39m Starting the server, please wait...
[2025-10-01T16:39:09.366Z] [INFO] [Pipeline Loader] Progress update: trellis - 0% - Waiting for Trellis server to start...
[2025-10-01T16:39:09.366Z] [INFO] [IPC Handler] Sending status: trellis - 0% - Waiting for Trellis server to start...
