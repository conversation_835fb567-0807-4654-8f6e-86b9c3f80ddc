<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AIStudio Test Page</h1>
        <div class="spinner"></div>
        <p>If you can see this, the HTML file loading is working correctly.</p>
        <p>The issue is likely with the React app or JavaScript loading.</p>
        <button onclick="testJS()">Test JavaScript</button>
        <div id="js-test-result"></div>
    </div>

    <script>
        console.log('Test HTML page loaded successfully');
        
        function testJS() {
            document.getElementById('js-test-result').innerHTML = 
                '<p style="color: #90EE90; margin-top: 10px;">✓ JavaScript is working!</p>';
            console.log('JavaScript test button clicked');
        }
        
        // Test if we can access the main app files
        fetch('./assets/index-C2aGS1Hp.js')
            .then(response => {
                console.log('Main JS file fetch status:', response.status);
                if (response.ok) {
                    console.log('✓ Main JS file is accessible');
                } else {
                    console.error('✗ Main JS file is not accessible');
                }
            })
            .catch(error => {
                console.error('Error fetching main JS file:', error);
            });
    </script>
</body>
</html>
