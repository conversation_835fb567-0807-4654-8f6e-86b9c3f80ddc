import {
  require_react
} from "./chunk-DRWLMN53.js";
import {
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/react-masonry-css/dist/react-masonry-css.module.js
var import_react = __toESM(require_react());
function _objectWithoutProperties(source, excluded) {
  if (source == null) return {};
  var target = _objectWithoutPropertiesLoose(source, excluded);
  var key, i;
  if (Object.getOwnPropertySymbols) {
    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);
    for (i = 0; i < sourceSymbolKeys.length; i++) {
      key = sourceSymbolKeys[i];
      if (excluded.indexOf(key) >= 0) continue;
      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;
      target[key] = source[key];
    }
  }
  return target;
}
function _objectWithoutPropertiesLoose(source, excluded) {
  if (source == null) return {};
  var target = {};
  var sourceKeys = Object.keys(source);
  var key, i;
  for (i = 0; i < sourceKeys.length; i++) {
    key = sourceKeys[i];
    if (excluded.indexOf(key) >= 0) continue;
    target[key] = source[key];
  }
  return target;
}
function _extends() {
  _extends = Object.assign || function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object);
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object);
    if (enumerableOnly) symbols = symbols.filter(function(sym) {
      return Object.getOwnPropertyDescriptor(object, sym).enumerable;
    });
    keys.push.apply(keys, symbols);
  }
  return keys;
}
function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {};
    if (i % 2) {
      ownKeys(Object(source), true).forEach(function(key) {
        _defineProperty(target, key, source[key]);
      });
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));
    } else {
      ownKeys(Object(source)).forEach(function(key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));
      });
    }
  }
  return target;
}
function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, { value, enumerable: true, configurable: true, writable: true });
  } else {
    obj[key] = value;
  }
  return obj;
}
var defaultProps = {
  breakpointCols: void 0,
  // optional, number or object { default: number, [key: number]: number }
  className: void 0,
  // required, string
  columnClassName: void 0,
  // optional, string
  // Any React children. Typically an array of JSX items
  children: void 0,
  // Custom attributes, however it is advised against
  // using these to prevent unintended issues and future conflicts
  // ...any other attribute, will be added to the container
  columnAttrs: void 0,
  // object, added to the columns
  // Deprecated props
  // The column property is deprecated.
  // It is an alias of the `columnAttrs` property
  column: void 0
};
var DEFAULT_COLUMNS = 2;
var Masonry = class extends import_react.default.Component {
  constructor(props) {
    super(props);
    this.reCalculateColumnCount = this.reCalculateColumnCount.bind(this);
    this.reCalculateColumnCountDebounce = this.reCalculateColumnCountDebounce.bind(this);
    let columnCount;
    if (this.props.breakpointCols && this.props.breakpointCols.default) {
      columnCount = this.props.breakpointCols.default;
    } else {
      columnCount = parseInt(this.props.breakpointCols) || DEFAULT_COLUMNS;
    }
    this.state = {
      columnCount
    };
  }
  componentDidMount() {
    this.reCalculateColumnCount();
    if (window) {
      window.addEventListener("resize", this.reCalculateColumnCountDebounce);
    }
  }
  componentDidUpdate() {
    this.reCalculateColumnCount();
  }
  componentWillUnmount() {
    if (window) {
      window.removeEventListener("resize", this.reCalculateColumnCountDebounce);
    }
  }
  reCalculateColumnCountDebounce() {
    if (!window || !window.requestAnimationFrame) {
      this.reCalculateColumnCount();
      return;
    }
    if (window.cancelAnimationFrame) {
      window.cancelAnimationFrame(this._lastRecalculateAnimationFrame);
    }
    this._lastRecalculateAnimationFrame = window.requestAnimationFrame(() => {
      this.reCalculateColumnCount();
    });
  }
  reCalculateColumnCount() {
    const windowWidth = window && window.innerWidth || Infinity;
    let breakpointColsObject = this.props.breakpointCols;
    if (typeof breakpointColsObject !== "object") {
      breakpointColsObject = {
        default: parseInt(breakpointColsObject) || DEFAULT_COLUMNS
      };
    }
    let matchedBreakpoint = Infinity;
    let columns = breakpointColsObject.default || DEFAULT_COLUMNS;
    for (let breakpoint in breakpointColsObject) {
      const optBreakpoint = parseInt(breakpoint);
      const isCurrentBreakpoint = optBreakpoint > 0 && windowWidth <= optBreakpoint;
      if (isCurrentBreakpoint && optBreakpoint < matchedBreakpoint) {
        matchedBreakpoint = optBreakpoint;
        columns = breakpointColsObject[breakpoint];
      }
    }
    columns = Math.max(1, parseInt(columns) || 1);
    if (this.state.columnCount !== columns) {
      this.setState({
        columnCount: columns
      });
    }
  }
  itemsInColumns() {
    const currentColumnCount = this.state.columnCount;
    const itemsInColumns = new Array(currentColumnCount);
    const items = import_react.default.Children.toArray(this.props.children);
    for (let i = 0; i < items.length; i++) {
      const columnIndex = i % currentColumnCount;
      if (!itemsInColumns[columnIndex]) {
        itemsInColumns[columnIndex] = [];
      }
      itemsInColumns[columnIndex].push(items[i]);
    }
    return itemsInColumns;
  }
  renderColumns() {
    const {
      column,
      columnAttrs = {},
      columnClassName
    } = this.props;
    const childrenInColumns = this.itemsInColumns();
    const columnWidth = `${100 / childrenInColumns.length}%`;
    let className = columnClassName;
    if (className && typeof className !== "string") {
      this.logDeprecated('The property "columnClassName" requires a string');
      if (typeof className === "undefined") {
        className = "my-masonry-grid_column";
      }
    }
    const columnAttributes = _objectSpread(_objectSpread(_objectSpread({}, column), columnAttrs), {}, {
      style: _objectSpread(_objectSpread({}, columnAttrs.style), {}, {
        width: columnWidth
      }),
      className
    });
    return childrenInColumns.map((items, i) => {
      return import_react.default.createElement("div", _extends({}, columnAttributes, {
        key: i
      }), items);
    });
  }
  logDeprecated(message) {
    console.error("[Masonry]", message);
  }
  render() {
    const _this$props = this.props, {
      // ignored
      children,
      breakpointCols,
      columnClassName,
      columnAttrs,
      column,
      // used
      className
    } = _this$props, rest = _objectWithoutProperties(_this$props, ["children", "breakpointCols", "columnClassName", "columnAttrs", "column", "className"]);
    let classNameOutput = className;
    if (typeof className !== "string") {
      this.logDeprecated('The property "className" requires a string');
      if (typeof className === "undefined") {
        classNameOutput = "my-masonry-grid";
      }
    }
    return import_react.default.createElement("div", _extends({}, rest, {
      className: classNameOutput
    }), this.renderColumns());
  }
};
Masonry.defaultProps = defaultProps;
var react_masonry_css_module_default = Masonry;
export {
  react_masonry_css_module_default as default
};
//# sourceMappingURL=react-masonry-css.js.map
