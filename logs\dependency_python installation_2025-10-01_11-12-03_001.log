========================================
AIStudio Real-time Log: dependency_core
Started: 2025-10-01T16:12:03.757Z
File: dependency_python installation_2025-10-01_11-12-03_001.log
========================================

[2025-10-01T16:12:03.757Z] [INFO] DependencyManager: Starting python installation for Core
[2025-10-01T16:12:03.757Z] [INFO] DependencyManager: Installing dependencies for Core (python:all)
[2025-10-01T16:12:03.758Z] [INFO] DependencyManager: Component type: string, Component value: 'python'
[2025-10-01T16:12:03.759Z] [INFO] DependencyManager: Name type: string, Name value: 'all'
[2025-10-01T16:12:03.759Z] [INFO] DependencyManager: About to check routing for Core with component python
[2025-10-01T16:12:03.760Z] [INFO] DependencyManager: Installing Python dependencies for Core...
[2025-10-01T16:16:03.949Z] [INFO] DependencyManager: No models configured for Core
[2025-10-01T16:16:06.531Z] [INFO] DependencyManager: Dependency status updated for Core
[2025-10-01T16:16:06.532Z] [INFO] DependencyManager: Completed python installation for Core
[2025-10-01T16:16:07.034Z] [INFO] DependencyManager: Sending dependency-status-changed event for Core (python)

========================================
Log ended: 2025-10-01T16:16:07.540Z
========================================
